<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Azure Notification Hubs, pricing details, pricing, billing" name="keywords"/>
    <meta content="Learn about the pricing details of Azure Notification Hubs. Azure Notification Hubs provide a highly extendable cross-platform notification push infrastructure, which can help you push and broadcast notifications to millions of users or provide custom-made notification service for users. A 1RMB Trial gets you RMB1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer."
          name="description"/>
    <title>
        Notification Hub Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/notification-hubs/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-notification-hubs" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/notifications_hubs.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Notification Hubs
                                </h2>
                                <h4>
                                    Send push notifications to any platform from any back end
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure Notification Hubs provides a highly scalable, cross-platform push notification infrastructure that enables you
                            to either broadcast push notifications to millions of users at once, or tailor notifications to individual users.
                            You can use Notification Hubs with any connected mobile application—whether it’s built on Azure Virtual Machines,
                            Cloud Services, Web Sites, or Mobile Services.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h2>
                                Pricing Details
                            </h2>
                            <!--<h3>Web 应用</h3>-->
                            <!-- <p>从 2016 年 4 月 1 日起，通知中心的价格下调 25.5%，以下是下调后的新价格：</p> -->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly price estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Free
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Basic
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Standard
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Basic charge
                                        <sup style="font-weight: normal;">
                                            1
                                        </sup>
                                        <br/>
                                        Included pushes
                                    </td>
                                    <td>
                                        Free
                                        <br/>
                                        1 million
                                        <sup style="font-weight: normal;">
                                            2
                                        </sup>
                                    </td>
                                    <td>
                                        ￥46.16 / month
                                        <br/>
                                        10 million
                                    </td>
                                    <td>
                                        ￥923.41 / month
                                        <br/>
                                        10 million
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Additional pushes
                                        <br/>
                                        10 million-100 million/month
                                        <br/>
                                        Over 100 million/month
                                    </td>
                                    <td>
                                        <br/>
                                        N/A
                                        <br/>
                                        N/A
                                    </td>
                                    <td>
                                        <br/>
                                        ￥4.61 / million
                                        <br/>
                                        ￥4.61 / million
                                    </td>
                                    <td>
                                        <br/>
                                        ￥46.16 / million
                                        <br/>
                                        ￥11.54/million
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Number of active devices
                                    </td>
                                    <td>
                                        Unlimited
                                    </td>
                                    <td>
                                        Unlimited
                                    </td>
                                    <td>
                                        Unlimited
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Basic-type x-plat push to individual devices
                                    </td>
                                    <td>
                                        √
                                    </td>
                                    <td>
                                        √
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Broadcast (label size)
                                    </td>
                                    <td>
                                        Limit: 10K
                                    </td>
                                    <td>
                                        Limit: 10K
                                    </td>
                                    <td>
                                        Unlimited
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Label number (broadcasting group)
                                    </td>
                                    <td>
                                        Limit: 3K
                                    </td>
                                    <td>
                                        Limit: 3K
                                    </td>
                                    <td>
                                        Unlimited
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Automatic scaling
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Queryable audience (registration queries)
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Scheduled push
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Telemetry
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Bulk import
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Multi-tenancy
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        √
                                    </td>
                                </tr>
                            </table>
                            <div class="tags-date">
                                <div class="ms-date">
                                    <strong>
                                        <sup style="font-weight: normal;">
                                            1
                                        </sup>
                                    </strong>
                                    The basic expenses prorated by 31 days/month.
                                </div>
                                <div class="ms-date">
                                    <strong>
                                        <sup style="font-weight: normal;">
                                            2
                                        </sup>
                                    </strong>
                                    The free level 1 million pushes is calculated on UTC time calendar month.
                                </div>
                            </div>
                            <!-- END: Table1-Content-->
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em id="notification_hubs_unfolded_all">
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs1">
                                            What is Azure Notification Hubs Baidu Cloud Messaging?
                                        </a>
                                        <section>
                                            <p>
                                                For application program developers, it is challenging to develop Android application programs aimed at the
                                                Chinese market that can receive pushed notifications. Android mobile phones usually do not have Google Play.
                                                However, these devices can only receive notifications from GCM (Google Cloud Messaging) through Google Play.
                                                Meanwhile, many different application stores and messaging services make this even more difficult.
                                            </p>
                                            <p>
                                                Today, we announce support for sending pushes from Azure Notification Hub to these kinds of Android devices
                                                through Baidu Cloud Messaging. This is in addition to existing support for iOS, Windows Phone, Windows,
                                                Android and Kindle devices in Azure Notification Hub.
                                            </p>
                                            <p>
                                                Application program developers must log in to Baidu portal, register themselves as Baidu developers, create a
                                                cloud messaging project and get the corresponding identifier for an application program (UserId and
                                                ChannelId). Then, they should insert the identifier into Azure Notification Hub on the Azure Management
                                                Portal. After that, they can use Notification Hub Android SDK updated in their client-side application
                                                program to register the device in Notification Hub, and then use updated Service Bus/Notification Hub.NET
                                                SDK to push notifications, which will be transmitted to the registered Android device through Baidu Cloud
                                                Messaging.
                                            </p>
                                            <p>
                                                <a href="https://docs.azure.cn/notification-hubs/notification-hubs-baidu-china-android-notifications-get-started"
                                                   id="Notification_price_cichu" style="color: #00a8d9;">
                                                    Here
                                                </a>
                                                is the detailed Tutorial for Beginners about development.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs2">
                                            What will happen to existing users of Notification Hubs after December 1, 2014?
                                        </a>
                                        <section>
                                            <p>
                                                Subscribers of Notification Hubs registered before December 1, 2014 will be automatically charged at the new pricing of
                                                the corresponding service. If a user has been using Basic services before, he will be automatically moved to a new basic
                                                class. For more help, please contact
                                                <a href="https://support.azure.cn/en-us/support/contact" id="Notification_price_newsuppert" style="color: #00a8d9;">
                                                    Support
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs3">
                                            How does the Basic layer automatically scale?
                                        </a>
                                        <section>
                                            <p>
                                                Before November 30, 2014, you can designate the minimum value of unit numbers for your Namespace on the
                                                Management Portal (to ensure enough capacity is left for active devices or push notifications exceeding the
                                                present level). You can also set the maximum unit number that you hope the service bus can use when it
                                                automatically scales according to the actual use level of Namespace. At the beginning of each day (00:00 UTC
                                                time), we will provide capacity not less than the minimum value set by you or the minimum capacity needed to
                                                support your current active devices. During the following whole day, if the number of your active devices or
                                                push notifications exceed the currently provided level, we will increase the capacity up to the maximum value
                                                chosen by you (before extra capacity is successfully supplied, you may experience throttling or a long delay).
                                                At the end of the day (at midnight UTC time), you will be charged by the maximum value of units used in the
                                                whole day. If you want to stop the automatic scaling function, you can set the same figure for maximum value and
                                                minimum value.
                                            </p>
                                            <p>
                                                From December 1, 2014 on, Basic and Standard services do not need automatic scaling. Customers can use limitless
                                                messaging function at the current published charge rate.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs4">
                                            Does Notification Hubs have any form of quota?
                                        </a>
                                        <section>
                                            <p>
                                                Before November 30, 2014, free services allowed daily average pushed notifications of 3,333 per day. Basic service was
                                                16,667 per day, a standard unit was 166,667 per day.
                                            </p>
                                            <p>
                                                After Namespace reached the cap for daily number of pushes and before the end of the day (midnight UTC time), or before
                                                more units were chosen in Standard service, or before it was upgraded to Premium service to raise the cap, the Namespace
                                                could no longer send notifications.
                                            </p>
                                            <p>
                                                For Standard services, the number of units can automatically scale.
                                            </p>
                                            <p>
                                                From December 1, 2014 on,the daily quota of pushes were invalidated due to changes in the cost of Basic expenses and
                                                messaging charges.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs5">
                                            Do free services have any caps for the number of active devices?
                                        </a>
                                        <section>
                                            <p>
                                                Before November 30, 2014, the number of active devices was constantly tracked. When the number of active devices
                                                reach the cap, registration operations for new devices would not succeed until the number of active devices fell
                                                below the cap (log off of registered devices through expiration operations), or until Standard service was
                                                upgraded or extended to raise the cap, or Basic or Standard units were expanded.
                                            </p>
                                            <p>
                                                From December 1, 2014 on, all service levels have no caps on the number of active devices.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs6">
                                            What is contained in every push?
                                        </a>
                                        <section>
                                            <p>
                                                The push contains all notifications provided for platform messaging services (e.g., Windows notification service,
                                                Apple Messaging Service, Google Cloud Messaging, Microsoft Push Notification Service).
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs7">
                                            What is an active device?
                                        </a>
                                        <section>
                                            <p>
                                                Active devices are devices that can receive notifications. Google Cloud Messaging or Amazon Device Messaging
                                                should be used to define a unique registration ID for such devices; or Windows Push Notification Service or
                                                Microsoft Push Notification Service should be used to register a URI (Uniform Resource Identifier); or Apple
                                                Push Notification Service is used to create device tokens. It is worth noting that a physical device can be
                                                regarded as multiple active devices in Notification Hubs.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs8">
                                            What is Broadcast and Label?
                                        </a>
                                        <section>
                                            <p>
                                                “Broadcast” refers to the number of devices that your push notifications can reach through a notification
                                                request. “Label” refers to the keyword subscribed by a device. The push notifications of Broadcast can be sent
                                                to all devices that have subscribed to certain labels.
                                            </p>
                                            <p>
                                                Before November 30, 2014, the Broadcast function limit was the same as the limit of the number of active devices
                                                at your Notification Hubs service level.
                                            </p>
                                            <p>
                                                From December 1, 2014, in terms of Free and Basic services of Notification Hubs, you can send a broadcast to a
                                                target audience with at most 10,000 devices. If the audience has more devices than that, 10,000 devices will be
                                                chosen at random as receiving devices and the rest of the devices cannot receive any notifications.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Notification_hubs9">
                                            What is a Namespace? How many units can be used by each Namespace in Basic and Standard service?
                                        </a>
                                        <section>
                                            <p>
                                                Namespace is a grouping mechanism which may contain multiple Notification Hubs. Before November 30, 2014, Azure
                                                Management Portal allowed customers to extend each Namespace to at most 50 units in Standard service. If your
                                                Namespace needs a larger capacity, please contact
                                                <a href="https://support.azure.cn/en-us/support/contact" id="Notification_price_suppert2">
                                                    Support
                                                </a>
                                                .
                                            </p>
                                            <p>
                                                From December 1, 2014, billing will be calculated at the Basic level cost plus the number of pushed notifications, so the
                                                unit limit will be canceled.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                            <p>
                                For other common questions about Notification Hubs, please refer to
                                <a href="http://msdn.microsoft.com/en-us/library/jj927170.aspx" id="Notification_price_MSDN" style="color: #00a8d9;">
                                    this MSDN article
                                </a>
                                .
                            </p>
                        </div>
                    </div>
                    <!-- <div class="pricing-page-section">
                             <h2>Region</h2>
                             <p>Notification Hubs is available in the following regions:</p>
                             <table cellpadding="0" cellspacing="0" class="table-col6">
                                 <tr>
                                     <th align="left"><strong>Territory</strong></th>
                                     <th align="left"><strong>Region</strong></th>
                                 </tr>
                                 <tr>
                                     <td>Mainland China</td>
                                     <td>China North, China East</td>
                                 </tr>
                             </table>
                              -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="notification-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            For the “Basic” and “Standard” levels of Notification Hubs, we guarantee that, at least 99.9% of the time,
                            Notification Hub services operating at Basic or Standard level can successfully send notifications or perform
                            operations about registration management through correctly configured application programs. To learn more about the
                            details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/notification-hubs" id="pricing_notification_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--


                         <h2>Support &amp; SLA</h2>
                         <p>Azure Support Features:</p>
                         <p>We provide users with the following free support services:</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left"> </th>
                                 <th align="left"><strong>Supported or not</strong></th>
                             </tr>
                             <tr>
                                 <td>Billing and Subscription Management</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Service Dashboard</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web Event Submission</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Unlimited Disruption/Restoration</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Telephone Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP Filing Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>You can <a href="/en-us/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                         <h2>Service hotline:</h2>
                         <ul>
                             <li>************</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="s6Q8kMA9sL2oMXxtppybaKQO9XDD5Up3VMxmotdrcAZRSatziDIECMeGL-MWNSplx3uoQ60PYNJFgwlRYopcsNjIT6ofjBmhWUZbLuy39qw1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
