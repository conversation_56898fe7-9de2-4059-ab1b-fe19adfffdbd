<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="WebSocket 实时 web, SignalR, PubSub 推送通知" name="keywords"/>
    <meta content="查看 Azure SignalR 服务的定价。借助 Azure SignalR 服务，可轻松构建和管理实时 Web 应用程序" name="description"/>
    <title>
        Azure SignalR 服务的定价 | Azure 云计算
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/signalr-service" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/12/9 7:37:32";
    window.currentLocale = "en-us";
    window.headerTimestamp = "2019/11/6 2:43:11";
    window.footerTimestamp = "2019/11/6 2:43:11";
    window.locFileTimestamp = "2019/11/6 2:43:05";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-signalr-service" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a, .pure-content .technical-azure-selector p a, .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/signalr-service_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Azure SignalR Service pricing
                                </h2>
                                <h4>
                                    Add real-time web functionalities easily
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <p>
                            Azure SignalR Service is a fully-managed service that allows developers to focus on building real-time web experiences without worrying about
                            capacity provisioning, reliable connections, scaling, encryption or authentication. Through tight integration with the .NET SignalR library
                            and Visual Studio family, the service makes it easier to deliver experiences such as chat, instant broadcasting, and IoT dashboards.
                        </p>
                        <h2>
                            Pricing details
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Signalr Service
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_signalr-service">
                                                        Signalr Service
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="Signalr Service">
                                                Signalr Service
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <!-- <label>Region:</label>
                                                    <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                        <span class="selected-item">China East 2</span>
                                                        <i class="icon"></i>
                                                        <ol class="tab-items">
                                                            <li class="active"><a href="javascript:void(0)" data-href="#east-china2" id="east-china2" >China East 2</a></li>
                                                            <li><a href="javascript:void(0)" data-href="#north-china2" id="north-china2" >China North 2</a></li>
                                                            <li><a href="javascript:void(0)" data-href="#east-china" id="east-china" >China East</a></li>
                                                            <li><a href="javascript:void(0)" data-href="#north-china" id="north-china" >China North</a></li>
                                                        </ol>
                                                    </div> -->
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china2" selected="selected" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">
                                    <p>
                                        SignalR comes in Free, Standard, and Premium tiers. Here is how they compare:
                                    </p>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly pricing estimates are based on 744 hours of usage per month.
                                        </div>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" id="signalr-service-table-free-standard" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    FREE
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Premium
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Concurrent Connections per Unit
                                            </td>
                                            <td>
                                                20
                                            </td>
                                            <td>
                                                1,000
                                            </td>
                                            <td>
                                                1,000
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Messages/Unit/Day
                                            </td>
                                            <td>
                                                20,000
                                            </td>
                                            <td>
                                                unlimited (free for the first 1,000,000 messages)
                                            </td>
                                            <td>
                                                unlimited (free for the first 1,000,000 messages)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Price/Unit/Day
                                            </td>
                                            <td>
                                                Free
                                            </td>
                                            <td>
                                                ¥ 16.38
                                            </td>
                                            <td>
                                                ¥ 20.346 (instance scale &lt;= 100 units)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                SLA
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                            <td>
                                                99.95%
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Availability Zone Support
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Fully managed auto scaling
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Customized domain name
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                -
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Max Units
                                            </td>
                                            <td>
                                                1
                                            </td>
                                            <td>
                                                100
                                            </td>
                                            <td>
                                                100
                                            </td>
                                        </tr>
                                    </table>
                                    <p>
                                        Additional Messages:
                                    </p>
                                    <p>
                                        Additional messages pay be purchased for the Standard Unit.
                                    </p>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly pricing estimates are based on 744 hours of usage per month.
                                        </div>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" id="signalr-service-table-others-information" width="100%">
                                        <tr>
                                            <td>
                                                Messages
                                            </td>
                                            <td>
                                                ¥ 10.176 per million messages
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <!-- <div class="pricing-page-section">
                             <div class="more-detail">
                                 <h2>FAQ</h2>
                                 <em>Expand All</em>
                                 <ul>
                                     <li>
                                         <i class="icon icon-plus"></i>
                                         <div>
                                             <a id="signalr-service_prepare-ssis">预配 SQL Server Integration Services (SSIS) 的集成运行时需要在 Azure SQL 数据库实例上创建一个 SSIS 目录。是否需要为该 SSIS 目录的 SQL 数据库实例单独付费？</a>
                                             <section>
                                                 <p>可以。必须单独为用于在 Azure 上承载 SSIS 目录的 SQL 数据库实例进行付费。可以使用现有的 SQL 数据库服务器或者在 Azure 订阅下新建一个。</p>
                                             </section>
                                         </div>
                                     </li>
                                     <li>
                                         <i class="icon icon-plus"></i>
                                         <div>
                                             <a id="signalr-service_can-I-use-sqlserver-hybrid">面向 SQL Server 的 Azure 混合权益是什么？我有资格使用它吗？</a>
                                             <section>
                                                 <p>面向 SQL Server 的 Azure 混合权益是旨在帮助你从 Azure SQL Server Integration Services 上的 SQL Server 许可证中收获更多价值的服务。如果客户具有有效软件保障 Enterprise 版和 Standard 版的每核心许可证并选择将这些许可证移动到云，那么他们就有资格使用此服务。面向 SQL Server 的 Azure 混合权益不限于任何特定许可计划，但客户必须具备有效的软件保障范围。</p>
                                                 <p>通过 Azure 门户证明你的软件保障带有足够的有效许可证，即可激活 Azure 混合权益。</p>
                                             </section>
                                         </div>
                                     </li>
                                     <li>
                                         <i class="icon icon-plus"></i>
                                         <div>
                                             <a id="signalr-service_face-to-sqlserver-bybrid">能否以追溯方式应用面向 SQL Server 的 Azure 混合权益？</a>
                                             <section>
                                                 <p>否。Azure 混合权益不能以追溯方式进行应用。</p>
                                             </section>
                                         </div>
                                     </li>
                                     <li>
                                         <i class="icon icon-plus"></i>
                                         <div>
                                             <a id="signalr-service_overdue-for-hybrid">如果软件保障到期，面向 SQL Server 的 Azure 混合权益会发生什么情况？</a>
                                             <section>
                                                 <p>若要使用面向 SQL Server 的 Azure 混合权益，需要有效的软件保障。如果软件保障到期并且没有续订，系统会将你转到相应 SKU 的“包含许可证”的定价。</p>
                                             </section>
                                         </div>
                                     </li>
                                     <li>
                                         <i class="icon icon-plus"></i>
                                         <div>
                                             <a id="signalr-service_">能否同时在本地和云中使用 SQL Server 许可证？</a>
                                             <section>
                                                 <p>为了便于进行迁移，Azure 提供了 180 天在本地和云中均可使用许可证的宽限期。在此之后，许可证必须只能在本地或云中使用。</p>
                                             </section>
                                         </div>
                                     </li>
                                 </ul>
                             </div>
                         </div> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="signalr-service-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/signalr-service" id="pricing_signalr-service_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--
             -->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<script type="text/javascript">
    (function () {
        if (typeof window.CustomEvent === "function") return false; //If not IE

        function CustomEvent(event, params) {
            params = params ||
                {
                    bubbles: false,
                    cancelable: false,
                    detail: undefined
                };
            var evt = document.createEvent('CustomEvent');
            evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
            return evt;
        }

        CustomEvent.prototype = window.Event.prototype;

        window.CustomEvent = CustomEvent;
    })();
</script>
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="OGT8btVYJJqM22ITgpOgfMq6Kft6pkqvN0U3Wu_OgsWek4u6H2s-GomzkVOW_vmh8A3ppTxx_DFGFSWaXyJKmDVrkebnO7G9B5EHg1VA4uo1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
