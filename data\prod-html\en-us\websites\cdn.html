<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure CDN pricing, data transfer and content delivery, CDN acceleration services website acceleration API acceleration live streaming acceleration application acceleration HTTPS HTTP2"
          name="keywords"/>
    <meta content="Learn more about the pricing details for Azure CDN. Azure CDN is mainly priced by CDN outbound data transfers. " name="description"/>
    <title>
        Content Delivery Network (CDN) Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/cdn/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-cdn" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/cdn.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/<EMAIL>"/>
                                <h2>
                                    Content Delivery Network (CDN)
                                </h2>
                                <h4>
                                    Ensure secure, reliable content delivery with broad reach
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure CDN improves application performance by caching content to the location nearest the customer.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <h2>
                            Pricing Details
                        </h2>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h3>
                                CDN outbound data transfers
                            </h3>
                            <!--<p>自 2016 年 12 月 1 月起，将取消阶梯式收费，CDN 数据传输价格将按照目前 0 - 10 TB/月的价格进行收取。</p>-->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly price estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Service
                                        </strong>
                                    </th>
                                    <th align="center" colspan="2">
                                        <strong>
                                            Standard version
                                            <sup>
                                                2
                                            </sup>
                                        </strong>
                                    </th>
                                    <th align="center">
                                        <strong>
                                            Standard Plus version
                                            <sup>
                                                3
                                            </sup>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Zone
                                    </td>
                                    <td align="center">
                                        Zone1
                                    </td>
                                    <td align="center">
                                        Zone2
                                    </td>
                                    <td align="center">
                                        Zone1
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        CDN data transfer (0-10 TB
                                        <sup>
                                            1
                                        </sup>
                                        /month)
                                    </td>
                                    <td align="center">
                                        ¥0.19/GB
                                    </td>
                                    <td align="center">
                                        ¥0.396/GB
                                    </td>
                                    <td align="center">
                                        ¥0.228/GB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        CDN data transfer (10-50 TB/month)
                                    </td>
                                    <td align="center">
                                        ¥0.16/GB
                                    </td>
                                    <td align="center">
                                        ¥0.347/GB
                                    </td>
                                    <td align="center">
                                        ¥0.198/GB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        CDN data transfer (50-100 TB/month)
                                    </td>
                                    <td align="center">
                                        ¥0.13/GB
                                    </td>
                                    <td align="center">
                                        ¥0.297/GB
                                    </td>
                                    <td align="center">
                                        ¥0.169/GB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        CDN data transfer (more than 100 TB/month)
                                    </td>
                                    <td align="center">
                                        ¥0.1/GB
                                    </td>
                                    <td align="center">
                                        ¥0.277/GB
                                    </td>
                                    <td align="center">
                                        ¥0.139/GB
                                    </td>
                                </tr>
                                <!--
                                              <tr>
                                                  <td>CDN 数据传输（500 - 1024 TB/月）</td>
                                                  <td>¥0.22/GB</td>
                                                  <td>¥0.44/GB</td>
                                              </tr>
                                              <tr>
                                                  <td>CDN 数据传输（1024 - 5120 TB/月）</td>
                                                  <td>¥0.19/GB</td>
                                                  <td>¥0.40/GB</td>
                                              </tr>
                                              <tr>
                                                  <td>CDN 数据传输（超过 5120 TB/月）</td>
                                                  <td><a id="CDN_contact_us" href="https://support.azure.cn/en-us/support/contact">联系我们</a></td>
                                                  <td><a id="CDN_price_us" href="https://support.azure.cn/en-us/support/contact">联系我们</a></td>
                                              </tr>
                                              -->
                            </table>
                            <div class="tags-date">
                                <div class="ms-date">
                                    <sup>
                                        1
                                    </sup>
                                    1 TB = 1000 GB
                                </div>
                                <br/>
                                <div class="ms-date">
                                    <sup>
                                        2
                                    </sup>
                                    Standard CDN service refers to static contents acceleration, including Web Acceleration, Download Acceleration and Video-On-Demand
                                    Acceleration.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    <sup>
                                        3
                                    </sup>
                                    Standard Plus CDN service refers to live-streaming acceleration.
                                </div>
                                <br/>
                            </div>
                            <!-- END: Table1-Content-->
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_region">
                                            Which regions correspond to Zone 1 and Zone 2?
                                        </a>
                                        <section>
                                            <p>
                                                CDN data transfer pricing is based on the node location from where the transfers are served, not the end user’s location.
                                                The following geographic areas correspond to the zones for Azure CDN as listed above ：
                                            </p>
                                            <p>
                                                • Zone 1 — Mainland China (Excluding Hong Kong, Taiwan and Macao).
                                            </p>
                                            <p>
                                                • Zone 2 — North America, Europe, Asia Pacific, Mid-East, Africa, South America, please refer to
                                                <a href="https://docs.azure.cn/cdn/cdn-pops">
                                                    oversea POP
                                                </a>
                                                for more information.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_question2">
                                            Can I designate a CDN data center to provide services to end users?
                                        </a>
                                        <section>
                                            <p>
                                                No. The service will select a CDN data center based on an end user's network configuration, developers cannot decide which
                                                CDN data center to use. Users will be provided services according to their ISP's preferred location or a logically
                                                determined "closer" (not necessarily physically closer) node.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_premium-version">
                                            What’s the difference between Standard CDN service, Standard Plus CDN service?
                                        </a>
                                        <section>
                                            <br/>
                                            <ul>
                                                <li>
                                                    Standard CDN service refers to static contents acceleration, including Web Acceleration, Download Acceleration and
                                                    Video-On-Demand Acceleration.
                                                </li>
                                                <li>
                                                    Standard Plus CDN service refers to live-streaming acceleration.
                                                </li>
                                            </ul>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_Peak_Value_Bandwidth_Billing_Explanation">
                                            Peak Value Bandwidth Billing Explanation
                                        </a>
                                        <section>
                                            <br/>
                                            <p>
                                                Peak value bandwidth billing mainly comprises monthly 95th percentile billing and average peak billing. You need to
                                                contact 21Vianet to set this up. The details are as follows:
                                            </p>
                                            <p>
                                                <strong>
                                                    1.Monthly 95th percentile billing
                                                </strong>
                                            </p>
                                            <p>
                                                The billing month refers to all the valid dates within a calendar month. A day with traffic &gt;0 is taken as a valid day.
                                                All the domains under the billed subscription will be aggregated for billing. The bandwidth data (Mbps) within a billing
                                                month is taken at every 5 minutes interval and sorted in descending order. The top 5 percentile of the sorted data
                                                are removed and the next bandwidth data is used as Max95. The billing for the month is Max95*unit price*valid
                                                days/calendar days in the billing month.
                                            </p>
                                            <p>
                                                For example, in one 30-day month that contains 20 valid days, the bandwidth data is taken every 5 minutes, and 12
                                                statistical points are taken every hour. The number of valid points is 12 x 24 x 20 = 5,760 points. All statistical points
                                                are then sorted in descending order and the first 5% (5,760 x 5% = 288) are removed, meaning that the billing point
                                                (Max95) is the 289th point, and the billing amount is Max95 * unit price*20/30.
                                            </p>
                                            <p>
                                                <strong>
                                                    2.Average peak billing
                                                </strong>
                                            </p>
                                            <p>
                                                The billing cycle is natural month. The peak bandwidth of the day &gt; 1kbps is recorded as effective days. Within a
                                                natural month, take the peak value of each effective day (&gt; 1kbps) for the billing subscription number, and take the
                                                average value to get the average peak bandwidth. The billing is the average peak bandwidth * 20 / 30.
                                            </p>
                                            <p>
                                                For example: if there are 20 valid days in a 30-day period, the highest value of each valid day is taken as that day’s
                                                active bandwidth. The sum of the values taken for all valid days is divided by the number of valid days, so the bill is
                                                calculated as follows: (Peak1 + Peak2 + ...+ Peak 20)/20*unit price*20/30.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_What_is_the_relationship_between_Content_Delivery_Network_flow_and_back-to-source_flow">
                                            What is the relationship between Content Delivery Network flow and back-to-source flow?
                                        </a>
                                        <section>
                                            <br/>
                                            <ul>
                                                <li>
                                                    Content Delivery Network traffic indicates cache hits.
                                                </li>
                                                <li>
                                                    Back-to-source traffic indicates the missed portion.
                                                </li>
                                            </ul>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_What_is_the_difference_between_log_traffic_and_paid_traffic">
                                            What is the difference between log traffic and paid traffic?
                                        </a>
                                        <section>
                                            <br/>
                                            <p>
                                                The amount of network traffic data generated by CDN-accelerated domain names is 7–15% higher than the amount of log
                                                traffic data. This difference occurs because log traffic data is counted as application-level traffic. The Internet
                                                transmission process not only includes app log traffic, but also additional network usage caused by TCP/IP headers and TCP
                                                retransmission.
                                            </p>
                                            <ul>
                                                <li>
                                                    TCP/IP header usage: Internet data packets based on the HTTP protocol are 1,500 bytes long, including the 40-character
                                                    headers for the TCP and IP protocols. Traffic generated by headers cannot be counted at the application level, so only
                                                    1,460 bytes of traffic are recorded in application-level logs. For this reason, packet headers account for 2.74%
                                                    (40/1,460) of application-level log traffic, so there is a data error of around 3%.
                                                </li>
                                                <li>
                                                    TCP retransmission: Fluctuations in Internet networks cause around 3–10% of data packets to be discarded by the
                                                    Internet. However, servers retransmit these lost data packets using the TCP protocol’s retransmission mechanism.
                                                    Retransmitted data traffic is also not counted by the application layer.
                                                </li>
                                            </ul>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_question4">
                                            What factors impact the availability of content in the CDN's local caches, and how can I reduce the need to make frequent
                                            origin requests?
                                        </a>
                                        <section>
                                            <p>
                                                The availability of content in the CDN's local caches (often called "cache efficacy" or "offload") will be influenced by
                                                many factors, including:
                                            </p>
                                            <ul>
                                                <li>
                                                    Expiration (“max-age”) header values
                                                </li>
                                                <li>
                                                    Overall total size of the developer's content library (how much can be cached)
                                                </li>
                                                <li>
                                                    Active working set (how much is currently cached)
                                                </li>
                                                <li>
                                                    Traffic (how much is being provided)
                                                </li>
                                                <li>
                                                    Cache churn (how often are objects being added to cache, or aging out)
                                                </li>
                                            </ul>
                                            <p>
                                                For example, a developer with higher churn and traffic has less cache efficacy than other users because objects are
                                                swapped in and out more frequently. This will lead to higher storage and data transfer charges, as more origin requests
                                                are required.
                                            </p>
                                            <p>
                                                To reduce the need to make origin requests, you can set longer max-age headers, thereby allowing the CDN to hold objects
                                                longer.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_higher-data-transmission">
                                            Are all data transfers charged at a higher rate once the amount of data transferred each month exceeds 10 TB?
                                        </a>
                                        <section>
                                            <p>
                                                No. Usage at each tier will be charged according to the rate for that tier. For example, if you generate 50 TB of Standard
                                                version CDN data transfers in Zone 1, the first 10 TB will be billed at a rate of ￥0.19/GB, and the remaining 40 TB will
                                                be billed at a rate of ￥0.16 per GB.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="CDN_price_question3">
                                            Do CDN charges include requests to Storage to retrieve data, and data transfers from Storage to the CDN location?
                                        </a>
                                        <section>
                                            <p>
                                                No. When the CDN receives an object request for a non-edge location, it will make a request to Azure Storage to obtain the
                                                data. Operations for reading data from Storage and transferring it from Storage to the CDN will be charged at standard
                                                <a href="/en-us/pricing/details/data-transfer/" id="CDN_price_data">
                                                    data transfer
                                                </a>
                                                rates.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <!-- <li>
                                                  <i class="icon icon-plus"></i>
                                                  <div>
                                                      <a id="CDN_price_question7">CDN 收费是否包括标准数据传输的费用？</a>
                                                      <section>
                                                          <p>CDN 回源请求会产生标准数据流量，即当 CDN 节点没有客户请求的内容时会直接向原站（Azure 存储）请求数据，从存储读取数据以及将数据传出到 CDN 节点的操作产生的流量就是普通流量，请求数据为入站流量，服务器（Azure 存储）响应客户端请求为出站流量。</p>
                                                      </section>

                                                  </div>
                                              </li>
                                              <li>
                                                  <i class="icon icon-plus"></i>
                                                  <div>
                                                      <a id="CDN_price_question8">什么时候会产生 CDN 的流量?</a>
                                                      <section>
                                                          <p>当客户端向 CDN 节点发出请求（传入）或 CDN 节点响应客户端的请求时（传出），会产生 CDN 流量。CDN 入站流量和 CDN 节点间流量不计费。</p>
                                                      </section>

                                                  </div>
                                              </li> -->
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="cdn-contact-page">
                                Azure Support
                            </a>
                            to select self-service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that CDN will respond to client requests and deliver the requested content without error at least 99.9% of the time. We will
                            examine and accept data from any commercially reasonable independent measurement system that you choose to monitor your content. You must
                            select a set of agents from the measurement system's list of standard agents that are generally available and represent at least five
                            different geographical locations in major metropolitan areas of the People's Republic of China. If you want to learn more about the details of
                            our server level agreement, please visit the
                            <a href="/en-us/support/sla/cdn/" id="pricing_cdn_sla">
                                Service Level Agreement
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="euhDjYYO9Ge_CgGNtIjf9YeUT6KgHc8UXd4EI6obJVU4b-Lcsmy9E620JnB-xAU9MLcfUQdvMJB6Q44Nn6u3aR2XcYPudNhZeJgLicXeU8Q1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
